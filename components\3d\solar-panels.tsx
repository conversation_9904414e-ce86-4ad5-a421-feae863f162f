"use client"
import { useRef } from "react"
import type { Mesh, Group } from "three"
import { useFrame } from "@react-three/fiber"
import { Text } from "@react-three/drei"

interface SolarPanelsProps {
  showLabels: boolean
  selectedPanel: number | null
  onPanelSelect: (index: number | null) => void
}

export function SolarPanels({ showLabels, selectedPanel, onPanelSelect }: SolarPanelsProps) {
  const groupRef = useRef<Group>(null)

  // Panel configuration - 4 rows of 6 panels each
  const panelRows = 4
  const panelsPerRow = 6
  const panelWidth = 1.2
  const panelHeight = 0.8
  const panelSpacing = 0.1

  // Calculate panel positions
  const panels = []
  for (let row = 0; row < panelRows; row++) {
    for (let col = 0; col < panelsPerRow; col++) {
      const x = (col - (panelsPerRow - 1) / 2) * (panelWidth + panelSpacing)
      const z = (row - (panelRows - 1) / 2) * (panelHeight + panelSpacing)
      panels.push({ x, z, index: row * panelsPerRow + col })
    }
  }

  // Gentle floating animation
  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.position.y = 1.7 + Math.sin(state.clock.elapsedTime * 0.5) * 0.02
    }
  })

  return (
    <group ref={groupRef}>
      {panels.map((panel) => (
        <SolarPanel
          key={panel.index}
          position={[panel.x, 0, panel.z]}
          index={panel.index}
          isSelected={selectedPanel === panel.index}
          showLabel={showLabels}
          onClick={() => onPanelSelect(panel.index)}
        />
      ))}
    </group>
  )
}

interface SolarPanelProps {
  position: [number, number, number]
  index: number
  isSelected: boolean
  showLabel: boolean
  onClick: () => void
}

function SolarPanel({ position, index, isSelected, showLabel, onClick }: SolarPanelProps) {
  const panelRef = useRef<Mesh>(null)

  // Hover animation
  useFrame((state) => {
    if (panelRef.current && isSelected) {
      panelRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2) * 0.05 + 0.1
    } else if (panelRef.current) {
      panelRef.current.position.y = position[1]
    }
  })

  return (
    <group position={position}>
      {/* Solar Panel */}
      <mesh
        ref={panelRef}
        castShadow
        onClick={onClick}
        onPointerOver={(e) => {
          e.stopPropagation()
          document.body.style.cursor = "pointer"
        }}
        onPointerOut={() => {
          document.body.style.cursor = "auto"
        }}
      >
        <boxGeometry args={[1.2, 0.05, 0.8]} />
        <meshStandardMaterial
          color={isSelected ? "#1e40af" : "#1e3a8a"}
          metalness={0.3}
          roughness={0.1}
          emissive={isSelected ? "#1e40af" : "#000000"}
          emissiveIntensity={isSelected ? 0.2 : 0}
        />
      </mesh>

      {/* Panel Frame */}
      <mesh castShadow position={[0, 0.03, 0]}>
        <boxGeometry args={[1.25, 0.02, 0.85]} />
        <meshStandardMaterial color="#2c2c2c" metalness={0.8} roughness={0.3} />
      </mesh>

      {/* Panel Grid Lines */}
      <group position={[0, 0.026, 0]}>
        {/* Vertical lines */}
        {[-0.3, 0, 0.3].map((x, i) => (
          <mesh key={`v-${i}`} position={[x, 0, 0]}>
            <boxGeometry args={[0.01, 0.001, 0.8]} />
            <meshStandardMaterial color="#4a5568" />
          </mesh>
        ))}
        {/* Horizontal lines */}
        {[-0.2, 0, 0.2].map((z, i) => (
          <mesh key={`h-${i}`} position={[0, 0, z]}>
            <boxGeometry args={[1.2, 0.001, 0.01]} />
            <meshStandardMaterial color="#4a5568" />
          </mesh>
        ))}
      </group>

      {/* Panel Label */}
      {showLabel && (
        <Text position={[0, 0.1, 0]} fontSize={0.1} color="#ffffff" anchorX="center" anchorY="middle">
          {index + 1}
        </Text>
      )}

      {/* Selection Indicator */}
      {isSelected && (
        <mesh position={[0, 0.08, 0]}>
          <ringGeometry args={[0.7, 0.75, 8]} />
          <meshBasicMaterial color="#fbbf24" transparent opacity={0.8} />
        </mesh>
      )}
    </group>
  )
}
