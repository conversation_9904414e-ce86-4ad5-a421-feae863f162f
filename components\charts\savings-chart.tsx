"use client"
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

const data = Array.from({ length: 26 }, (_, i) => ({
  year: i,
  savings: i === 0 ? 0 : Math.round(1867 * i * (1 + 0.03) ** i), // 3% annual increase
  cumulative:
    i === 0 ? 0 : Math.round(1867 * i * (1 + 0.03) ** i + (i > 1 ? 1867 * (i - 1) * (1 + 0.03) ** (i - 1) : 0)),
}))

export function SavingsChart() {
  return (
    <div className="space-y-4">
      <div className="text-center">
        <div className="text-3xl font-bold bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent">
          $46,675
        </div>
        <div className="text-sm text-muted-foreground">25-Year Savings</div>
      </div>

      <ChartContainer
        config={{
          cumulative: {
            label: "Cumulative Savings",
            color: "hsl(var(--chart-3))",
          },
        }}
        className="h-64"
      >
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={data}>
            <defs>
              <linearGradient id="savingsGradient" x1="0" y1="0" x2="1" y2="0">
                <stop offset="0%" stopColor="#f59e0b" />
                <stop offset="100%" stopColor="#ef4444" />
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
            <XAxis dataKey="year" stroke="#64748b" />
            <YAxis stroke="#64748b" tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`} />
            <ChartTooltip
              content={<ChartTooltipContent />}
              formatter={(value) => [`$${value.toLocaleString()}`, "Cumulative Savings"]}
            />
            <Line
              type="monotone"
              dataKey="cumulative"
              stroke="url(#savingsGradient)"
              strokeWidth={3}
              dot={{ fill: "#f59e0b", strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: "#f59e0b", strokeWidth: 2 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </ChartContainer>
    </div>
  )
}
