"use client"

import * as React from "react"
import { Moon, Sun, MapPin, Zap, FileText, Home, Menu, X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { AddressInput } from "@/components/pages/address-input"
import { RoofDetection } from "@/components/pages/roof-detection"
import { SolarAnalysis } from "@/components/pages/solar-analysis"
import { Report } from "@/components/pages/report"
import { motion, AnimatePresence } from "framer-motion"

const navigationItems = [
  {
    title: "Address Input",
    icon: MapPin,
    id: "address",
    gradient: "from-emerald-400 via-teal-500 to-blue-500",
    description: "Find your property",
  },
  {
    title: "Roof Detection",
    icon: Home,
    id: "roof",
    gradient: "from-blue-400 via-purple-500 to-indigo-600",
    description: "Analyze roof structure",
  },
  {
    title: "Solar Analysis",
    icon: Zap,
    id: "analysis",
    gradient: "from-amber-400 via-orange-500 to-red-500",
    description: "Calculate solar potential",
  },
  {
    title: "Report",
    icon: FileText,
    id: "report",
    gradient: "from-purple-400 via-pink-500 to-rose-500",
    description: "Generate detailed report",
  },
]

export function SolarDashboard() {
  const [activeTab, setActiveTab] = React.useState("address")
  const [isDarkMode, setIsDarkMode] = React.useState(false)
  const [sidebarOpen, setSidebarOpen] = React.useState(false)

  React.useEffect(() => {
    if (isDarkMode) {
      document.documentElement.classList.add("dark")
    } else {
      document.documentElement.classList.remove("dark")
    }
  }, [isDarkMode])

  const renderContent = () => {
    switch (activeTab) {
      case "address":
        return <AddressInput />
      case "roof":
        return <RoofDetection />
      case "analysis":
        return <SolarAnalysis />
      case "report":
        return <Report />
      default:
        return <AddressInput />
    }
  }

  const activeItem = navigationItems.find((item) => item.id === activeTab)

  return (
    <div className="flex min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-indigo-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl animate-pulse" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-emerald-400/20 to-teal-600/20 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-amber-400/10 to-orange-600/10 rounded-full blur-3xl animate-pulse delay-2000" />
      </div>

      {/* Mobile Menu Button */}
      <Button
        variant="ghost"
        size="icon"
        onClick={() => setSidebarOpen(!sidebarOpen)}
        className="fixed top-4 left-4 z-50 lg:hidden bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-300"
      >
        {sidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
      </Button>

      {/* Desktop Sidebar - Always visible on large screens */}
      <div className="hidden lg:block w-80 h-full bg-white/10 dark:bg-slate-900/10 backdrop-blur-2xl border-r border-white/20 shadow-2xl">
        {/* Header */}
        <div className="p-8 border-b border-white/10">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <motion.div
                whileHover={{ scale: 1.1, rotate: 180 }}
                transition={{ type: "spring", damping: 15 }}
                className="flex h-12 w-12 items-center justify-center rounded-2xl bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 shadow-lg"
              >
                <Zap className="h-6 w-6 text-white" />
              </motion.div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                  RoofSnap
                </h1>
                <p className="text-sm text-muted-foreground">Solar Intelligence Platform</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsDarkMode(!isDarkMode)}
              className="h-10 w-10 rounded-xl hover:bg-white/20 dark:hover:bg-slate-800/20 transition-all duration-300"
            >
              <motion.div whileHover={{ rotate: 180 }} transition={{ duration: 0.3 }}>
                {isDarkMode ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
              </motion.div>
            </Button>
          </div>

          {/* Progress Indicator */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Analysis Progress</span>
              <span className="font-medium">{navigationItems.findIndex((item) => item.id === activeTab) + 1}/4</span>
            </div>
            <div className="w-full bg-white/20 rounded-full h-2">
              <motion.div
                className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{
                  width: `${((navigationItems.findIndex((item) => item.id === activeTab) + 1) / 4) * 100}%`,
                }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
              />
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="p-6 space-y-3">
          {navigationItems.map((item, index) => (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <motion.button
                whileHover={{ scale: 1.02, x: 4 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => setActiveTab(item.id)}
                className={`w-full p-4 rounded-2xl transition-all duration-300 text-left group ${
                  activeTab === item.id
                    ? `bg-gradient-to-r ${item.gradient} text-white shadow-xl`
                    : "hover:bg-white/10 dark:hover:bg-slate-800/10"
                }`}
              >
                <div className="flex items-center space-x-4">
                  <div
                    className={`p-2 rounded-xl transition-all duration-300 ${
                      activeTab === item.id ? "bg-white/20" : "bg-white/10 dark:bg-slate-800/10 group-hover:bg-white/20"
                    }`}
                  >
                    <item.icon className="h-5 w-5" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold">{item.title}</h3>
                    <p className={`text-sm ${activeTab === item.id ? "text-white/80" : "text-muted-foreground"}`}>
                      {item.description}
                    </p>
                  </div>
                </div>
              </motion.button>
            </motion.div>
          ))}
        </div>

        {/* Footer */}
        <div className="absolute bottom-6 left-6 right-6">
          <div className="p-4 rounded-2xl bg-gradient-to-r from-emerald-500/10 to-teal-500/10 border border-emerald-500/20">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse" />
              <div>
                <p className="text-sm font-medium">System Status</p>
                <p className="text-xs text-muted-foreground">All systems operational</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Sidebar Overlay */}
      <AnimatePresence>
        {sidebarOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setSidebarOpen(false)}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
            />

            {/* Mobile Sidebar */}
            <motion.div
              initial={{ x: -320, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: -320, opacity: 0 }}
              transition={{ type: "spring", damping: 25, stiffness: 200 }}
              className="fixed left-0 top-0 z-50 h-full w-80 bg-white/95 dark:bg-slate-900/95 backdrop-blur-2xl border-r border-white/20 shadow-2xl lg:hidden"
            >
              {/* Mobile Header */}
              <div className="p-8 border-b border-white/10">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-4">
                    <motion.div
                      whileHover={{ scale: 1.1, rotate: 180 }}
                      transition={{ type: "spring", damping: 15 }}
                      className="flex h-12 w-12 items-center justify-center rounded-2xl bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 shadow-lg"
                    >
                      <Zap className="h-6 w-6 text-white" />
                    </motion.div>
                    <div>
                      <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                        RoofSnap
                      </h1>
                      <p className="text-sm text-muted-foreground">Solar Intelligence Platform</p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setIsDarkMode(!isDarkMode)}
                    className="h-10 w-10 rounded-xl hover:bg-white/20 dark:hover:bg-slate-800/20 transition-all duration-300"
                  >
                    <motion.div whileHover={{ rotate: 180 }} transition={{ duration: 0.3 }}>
                      {isDarkMode ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
                    </motion.div>
                  </Button>
                </div>

                {/* Progress Indicator */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Analysis Progress</span>
                    <span className="font-medium">
                      {navigationItems.findIndex((item) => item.id === activeTab) + 1}/4
                    </span>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2">
                    <motion.div
                      className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
                      initial={{ width: 0 }}
                      animate={{
                        width: `${((navigationItems.findIndex((item) => item.id === activeTab) + 1) / 4) * 100}%`,
                      }}
                      transition={{ duration: 0.5, ease: "easeInOut" }}
                    />
                  </div>
                </div>
              </div>

              {/* Mobile Navigation */}
              <div className="p-6 space-y-3">
                {navigationItems.map((item, index) => (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <motion.button
                      whileHover={{ scale: 1.02, x: 4 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => {
                        setActiveTab(item.id)
                        setSidebarOpen(false)
                      }}
                      className={`w-full p-4 rounded-2xl transition-all duration-300 text-left group ${
                        activeTab === item.id
                          ? `bg-gradient-to-r ${item.gradient} text-white shadow-xl`
                          : "hover:bg-white/10 dark:hover:bg-slate-800/10"
                      }`}
                    >
                      <div className="flex items-center space-x-4">
                        <div
                          className={`p-2 rounded-xl transition-all duration-300 ${
                            activeTab === item.id
                              ? "bg-white/20"
                              : "bg-white/10 dark:bg-slate-800/10 group-hover:bg-white/20"
                          }`}
                        >
                          <item.icon className="h-5 w-5" />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold">{item.title}</h3>
                          <p className={`text-sm ${activeTab === item.id ? "text-white/80" : "text-muted-foreground"}`}>
                            {item.description}
                          </p>
                        </div>
                      </div>
                    </motion.button>
                  </motion.div>
                ))}
              </div>

              {/* Mobile Footer */}
              <div className="absolute bottom-6 left-6 right-6">
                <div className="p-4 rounded-2xl bg-gradient-to-r from-emerald-500/10 to-teal-500/10 border border-emerald-500/20">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse" />
                    <div>
                      <p className="text-sm font-medium">System Status</p>
                      <p className="text-xs text-muted-foreground">All systems operational</p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-h-screen">
        {/* Header */}
        <motion.header
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="h-20 flex items-center justify-between px-8 bg-white/10 dark:bg-slate-900/10 backdrop-blur-2xl border-b border-white/10 shadow-lg"
        >
          <div className="flex items-center space-x-4 ml-12 lg:ml-0">
            {activeItem && (
              <>
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  className={`p-3 rounded-xl bg-gradient-to-r ${activeItem.gradient} shadow-lg`}
                >
                  <activeItem.icon className="h-6 w-6 text-white" />
                </motion.div>
                <div>
                  <h1 className="text-2xl font-bold">{activeItem.title}</h1>
                  <p className="text-sm text-muted-foreground">{activeItem.description}</p>
                </div>
              </>
            )}
          </div>

          <div className="flex items-center space-x-4">
            <div className="hidden md:flex items-center space-x-2 px-4 py-2 bg-white/10 dark:bg-slate-800/10 rounded-full">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              <span className="text-sm font-medium">Live Analysis</span>
            </div>
          </div>
        </motion.header>

        {/* Content */}
        <main className="flex-1 overflow-auto">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              {renderContent()}
            </motion.div>
          </AnimatePresence>
        </main>
      </div>
    </div>
  )
}
