"use client"

import * as React from "react"
import { Download, FileText, ChevronDown, ChevronRight, DollarSign, Zap, Home, Calendar } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"

export function Report() {
  const [technicalDataOpen, setTechnicalDataOpen] = React.useState(false)
  const [financialDataOpen, setFinancialDataOpen] = React.useState(false)

  return (
    <div className="p-6 space-y-6">
      <div className="max-w-2xl">
        <h2 className="text-2xl font-bold mb-2">Solar Analysis Report</h2>
        <p className="text-muted-foreground mb-6">
          Complete solar feasibility report with technical specifications and financial projections.
        </p>
      </div>

      {/* Executive Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-primary" />
              <div>
                <p className="text-sm text-muted-foreground">System Size</p>
                <p className="text-xl font-bold">8.4 kW</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Home className="h-5 w-5 text-primary" />
              <div>
                <p className="text-sm text-muted-foreground">Annual Production</p>
                <p className="text-xl font-bold">12,450 kWh</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-primary" />
              <div>
                <p className="text-sm text-muted-foreground">Annual Savings</p>
                <p className="text-xl font-bold">$1,867</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-primary" />
              <div>
                <p className="text-sm text-muted-foreground">Payback Period</p>
                <p className="text-xl font-bold">7.2 years</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Download Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Download Reports
          </CardTitle>
          <CardDescription>Export detailed analysis reports in multiple formats</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button className="justify-start h-auto p-4">
              <div className="flex items-center gap-3">
                <Download className="h-5 w-5" />
                <div className="text-left">
                  <div className="font-medium">PDF Report</div>
                  <div className="text-sm text-muted-foreground">Complete analysis with visualizations</div>
                </div>
              </div>
            </Button>

            <Button variant="outline" className="justify-start h-auto p-4 bg-transparent">
              <div className="flex items-center gap-3">
                <Download className="h-5 w-5" />
                <div className="text-left">
                  <div className="font-medium">CSV Data</div>
                  <div className="text-sm text-muted-foreground">Raw data for further analysis</div>
                </div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* System Overview */}
      <Card>
        <CardHeader>
          <CardTitle>System Overview</CardTitle>
          <CardDescription>Recommended solar panel system configuration</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h4 className="font-medium">System Specifications</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Number of Panels:</span>
                  <span className="font-medium">24 panels</span>
                </div>
                <div className="flex justify-between">
                  <span>Panel Wattage:</span>
                  <span className="font-medium">350W each</span>
                </div>
                <div className="flex justify-between">
                  <span>Total System Size:</span>
                  <span className="font-medium">8.4 kW DC</span>
                </div>
                <div className="flex justify-between">
                  <span>Inverter Type:</span>
                  <span className="font-medium">String Inverter</span>
                </div>
                <div className="flex justify-between">
                  <span>Estimated Installation:</span>
                  <span className="font-medium">1-2 days</span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="font-medium">Performance Metrics</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>System Efficiency:</span>
                  <Badge variant="default">87.3%</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Performance Ratio:</span>
                  <Badge variant="secondary">0.85</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Capacity Factor:</span>
                  <Badge variant="secondary">16.9%</Badge>
                </div>
                <div className="flex justify-between">
                  <span>First Year Degradation:</span>
                  <Badge variant="outline">2.5%</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Annual Degradation:</span>
                  <Badge variant="outline">0.5%</Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Collapsible Technical Data */}
      <Collapsible open={technicalDataOpen} onOpenChange={setTechnicalDataOpen}>
        <Card>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
              <CardTitle className="flex items-center justify-between">
                <span>Technical Data</span>
                {technicalDataOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
              </CardTitle>
              <CardDescription>Detailed technical specifications and calculations</CardDescription>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="font-medium">Solar Resource Data</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Global Horizontal Irradiance:</span>
                      <span className="font-medium">1,898 kWh/m²/year</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Direct Normal Irradiance:</span>
                      <span className="font-medium">2,156 kWh/m²/year</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Diffuse Horizontal Irradiance:</span>
                      <span className="font-medium">742 kWh/m²/year</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Peak Sun Hours:</span>
                      <span className="font-medium">5.2 hours/day</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium">System Losses</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Soiling Losses:</span>
                      <span className="font-medium">2.0%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Shading Losses:</span>
                      <span className="font-medium">1.5%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>DC Wiring Losses:</span>
                      <span className="font-medium">2.0%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>AC Wiring Losses:</span>
                      <span className="font-medium">1.0%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Inverter Losses:</span>
                      <span className="font-medium">3.5%</span>
                    </div>
                    <div className="flex justify-between border-t pt-2">
                      <span className="font-medium">Total System Losses:</span>
                      <span className="font-medium">9.7%</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </CollapsibleContent>
        </Card>
      </Collapsible>

      {/* Collapsible Financial Data */}
      <Collapsible open={financialDataOpen} onOpenChange={setFinancialDataOpen}>
        <Card>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
              <CardTitle className="flex items-center justify-between">
                <span>Financial Analysis</span>
                {financialDataOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
              </CardTitle>
              <CardDescription>Cost analysis and return on investment calculations</CardDescription>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="font-medium">System Costs</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Equipment Cost:</span>
                      <span className="font-medium">$8,400</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Installation Cost:</span>
                      <span className="font-medium">$4,200</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Permits & Inspection:</span>
                      <span className="font-medium">$800</span>
                    </div>
                    <div className="flex justify-between border-t pt-2">
                      <span className="font-medium">Total System Cost:</span>
                      <span className="font-medium">$13,400</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Federal Tax Credit (30%):</span>
                      <span className="font-medium text-green-600">-$4,020</span>
                    </div>
                    <div className="flex justify-between border-t pt-2">
                      <span className="font-medium">Net System Cost:</span>
                      <span className="font-medium">$9,380</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium">Financial Returns</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Annual Energy Savings:</span>
                      <span className="font-medium">$1,867</span>
                    </div>
                    <div className="flex justify-between">
                      <span>25-Year Savings:</span>
                      <span className="font-medium">$46,675</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Net Present Value:</span>
                      <span className="font-medium">$28,450</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Internal Rate of Return:</span>
                      <span className="font-medium">13.8%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Payback Period:</span>
                      <span className="font-medium">7.2 years</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </CollapsibleContent>
        </Card>
      </Collapsible>
    </div>
  )
}
