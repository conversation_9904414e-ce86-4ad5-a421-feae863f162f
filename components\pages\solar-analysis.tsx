"use client"

import * as React from "react"
import { Sun, Zap, TrendingUp, Battery, Leaf, DollarSign, BarChart3 } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Roof3DVisualization } from "@/components/3d/roof-3d-visualization"
import { EnergyChart } from "@/components/charts/energy-chart"
import { SavingsChart } from "@/components/charts/savings-chart"
import { IrradianceChart } from "@/components/charts/irradiance-chart"
import { motion } from "framer-motion"

export function SolarAnalysis() {
  const [analysisProgress, setAnalysisProgress] = React.useState(0)
  const [isAnalyzing, setIsAnalyzing] = React.useState(true)
  const [currentStep, setCurrentStep] = React.useState(0)

  const analysisSteps = [
    { label: "Satellite Analysis", icon: "🛰️", description: "Processing roof geometry" },
    { label: "Solar Irradiance", icon: "☀️", description: "Calculating sun exposure" },
    { label: "Shading Analysis", icon: "🌳", description: "Detecting obstructions" },
    { label: "Weather Modeling", icon: "🌤️", description: "Historical weather data" },
    { label: "Financial Modeling", icon: "💰", description: "Estimating savings" },
  ]

  React.useEffect(() => {
    if (isAnalyzing) {
      const interval = setInterval(() => {
        setAnalysisProgress((prev) => {
          const newProgress = prev + 0.8
          const newStep = Math.floor((newProgress / 100) * analysisSteps.length)
          setCurrentStep(Math.min(newStep, analysisSteps.length - 1))

          if (newProgress >= 100) {
            setIsAnalyzing(false)
            clearInterval(interval)
            return 100
          }
          return newProgress
        })
      }, 50)
      return () => clearInterval(interval)
    }
  }, [isAnalyzing])

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        damping: 20,
        stiffness: 100,
      },
    },
  }

  return (
    <motion.div variants={containerVariants} initial="hidden" animate="visible" className="p-8 space-y-8">
      {/* Hero Section */}
      <motion.div variants={itemVariants} className="text-center space-y-6 py-8">
        <motion.div
          whileHover={{ scale: 1.05, rotate: 5 }}
          transition={{ type: "spring", damping: 15 }}
          className="inline-flex items-center justify-center w-20 h-20 rounded-3xl bg-gradient-to-br from-amber-500 via-orange-500 to-red-500 shadow-2xl"
        >
          <Zap className="h-10 w-10 text-white" />
        </motion.div>
        <div className="space-y-4">
          <h1 className="text-5xl font-bold bg-gradient-to-r from-amber-600 via-orange-600 to-red-600 bg-clip-text text-transparent">
            Solar Intelligence Analysis
          </h1>
          <p className="text-xl text-muted-foreground max-w-4xl mx-auto">
            Advanced AI-powered analysis combining satellite imagery, weather patterns, and financial modeling to
            optimize your solar investment.
          </p>
        </div>
      </motion.div>

      {isAnalyzing && (
        <motion.div variants={itemVariants}>
          <Card className="border-0 shadow-2xl bg-gradient-to-br from-amber-50/50 to-orange-50/50 dark:from-amber-950/20 dark:to-orange-950/20 backdrop-blur-2xl overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-amber-500/5 via-orange-500/5 to-red-500/5" />
            <CardHeader className="text-center relative">
              <CardTitle className="flex items-center justify-center gap-4 text-3xl">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
                >
                  <Sun className="h-10 w-10 text-amber-500" />
                </motion.div>
                AI Analysis in Progress
              </CardTitle>
              <CardDescription className="text-lg mt-2">
                Our advanced algorithms are processing your property data
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-8 relative">
              <div className="space-y-4">
                <div className="flex justify-between text-lg font-medium">
                  <span>Analysis Progress</span>
                  <span>{Math.round(analysisProgress)}%</span>
                </div>
                <div className="relative">
                  <Progress value={analysisProgress} className="h-4 bg-amber-100 dark:bg-amber-950" />
                  <motion.div
                    className="absolute top-0 left-0 h-4 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${analysisProgress}%` }}
                    transition={{ duration: 0.5, ease: "easeOut" }}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                {analysisSteps.map((step, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0.3, scale: 0.9 }}
                    animate={{
                      opacity: index <= currentStep ? 1 : 0.3,
                      scale: index === currentStep ? 1.05 : index < currentStep ? 1 : 0.9,
                    }}
                    transition={{ duration: 0.3 }}
                    className={`text-center p-4 rounded-2xl transition-all duration-300 ${
                      index === currentStep
                        ? "bg-gradient-to-br from-amber-100 to-orange-100 dark:from-amber-900/30 dark:to-orange-900/30 shadow-lg"
                        : index < currentStep
                          ? "bg-green-50 dark:bg-green-900/20"
                          : "bg-gray-50 dark:bg-gray-800/20"
                    }`}
                  >
                    <div className="text-3xl mb-2">{step.icon}</div>
                    <p className="font-semibold text-sm">{step.label}</p>
                    <p className="text-xs text-muted-foreground mt-1">{step.description}</p>
                    {index < currentStep && (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="mt-2 w-6 h-6 bg-green-500 rounded-full mx-auto flex items-center justify-center"
                      >
                        <span className="text-white text-xs">✓</span>
                      </motion.div>
                    )}
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {!isAnalyzing && (
        <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ delay: 0.5 }} className="space-y-8">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {[
              {
                icon: Zap,
                title: "System Size",
                value: "8.4 kW",
                subtitle: "24 premium panels",
                gradient: "from-blue-500 via-indigo-500 to-purple-600",
                bgGradient: "from-blue-50/50 to-purple-50/50 dark:from-blue-950/20 dark:to-purple-950/20",
                change: "+12%",
              },
              {
                icon: Battery,
                title: "Annual Production",
                value: "12,450",
                subtitle: "kWh per year",
                gradient: "from-emerald-500 via-teal-500 to-cyan-600",
                bgGradient: "from-emerald-50/50 to-cyan-50/50 dark:from-emerald-950/20 dark:to-cyan-950/20",
                change: "+8%",
              },
              {
                icon: DollarSign,
                title: "Annual Savings",
                value: "$1,867",
                subtitle: "in electricity costs",
                gradient: "from-amber-500 via-orange-500 to-red-500",
                bgGradient: "from-amber-50/50 to-red-50/50 dark:from-amber-950/20 dark:to-red-950/20",
                change: "+15%",
              },
              {
                icon: Leaf,
                title: "CO₂ Offset",
                value: "8.7 tons",
                subtitle: "per year",
                gradient: "from-green-500 via-emerald-500 to-teal-600",
                bgGradient: "from-green-50/50 to-teal-50/50 dark:from-green-950/20 dark:to-teal-950/20",
                change: "+10%",
              },
            ].map((metric, index) => (
              <motion.div
                key={index}
                initial={{ y: 50, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: index * 0.1, type: "spring", damping: 20 }}
                whileHover={{ y: -5, scale: 1.02 }}
              >
                <Card
                  className={`border-0 shadow-xl bg-gradient-to-br ${metric.bgGradient} backdrop-blur-2xl hover:shadow-2xl transition-all duration-300 overflow-hidden group`}
                >
                  <div
                    className={`absolute inset-0 bg-gradient-to-br ${metric.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}
                  />
                  <CardContent className="p-6 text-center space-y-4 relative">
                    <div className="flex items-center justify-between">
                      <motion.div
                        whileHover={{ rotate: 360, scale: 1.1 }}
                        transition={{ duration: 0.5 }}
                        className={`inline-flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-br ${metric.gradient} shadow-lg`}
                      >
                        <metric.icon className="h-6 w-6 text-white" />
                      </motion.div>
                      <Badge
                        variant="secondary"
                        className="bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
                      >
                        {metric.change}
                      </Badge>
                    </div>
                    <div className="space-y-1">
                      <p className="text-3xl font-bold">{metric.value}</p>
                      <p className="text-sm font-medium text-muted-foreground">{metric.title}</p>
                      <p className="text-xs text-muted-foreground">{metric.subtitle}</p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {/* Charts and 3D Visualization */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* 3D Visualization */}
            <motion.div initial={{ x: -50, opacity: 0 }} animate={{ x: 0, opacity: 1 }} transition={{ delay: 0.6 }}>
              <Card className="border-0 shadow-2xl bg-white/20 dark:bg-slate-900/20 backdrop-blur-2xl overflow-hidden">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center">
                      <Sun className="h-4 w-4 text-white" />
                    </div>
                    3D Panel Layout
                    <Badge
                      variant="secondary"
                      className="ml-auto bg-gradient-to-r from-blue-500 to-indigo-600 text-white"
                    >
                      Interactive
                    </Badge>
                  </CardTitle>
                  <CardDescription>Real-time 3D visualization with performance data</CardDescription>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="aspect-square w-full">
                    <Roof3DVisualization />
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Charts */}
            <div className="space-y-6">
              <motion.div initial={{ x: 50, opacity: 0 }} animate={{ x: 0, opacity: 1 }} transition={{ delay: 0.7 }}>
                <Card className="border-0 shadow-xl bg-gradient-to-br from-emerald-50/50 to-teal-50/50 dark:from-emerald-950/20 dark:to-teal-950/20 backdrop-blur-2xl">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-3">
                      <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-emerald-500 to-teal-600 flex items-center justify-center">
                        <BarChart3 className="h-4 w-4 text-white" />
                      </div>
                      Energy Production
                    </CardTitle>
                    <CardDescription>Monthly energy generation forecast</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <EnergyChart />
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div initial={{ x: 50, opacity: 0 }} animate={{ x: 0, opacity: 1 }} transition={{ delay: 0.8 }}>
                <Card className="border-0 shadow-xl bg-gradient-to-br from-amber-50/50 to-orange-50/50 dark:from-amber-950/20 dark:to-orange-950/20 backdrop-blur-2xl">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-3">
                      <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-amber-500 to-orange-600 flex items-center justify-center">
                        <DollarSign className="h-4 w-4 text-white" />
                      </div>
                      Financial Savings
                    </CardTitle>
                    <CardDescription>Cumulative savings over 25 years</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <SavingsChart />
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </div>

          {/* Irradiance Chart */}
          <motion.div initial={{ y: 50, opacity: 0 }} animate={{ y: 0, opacity: 1 }} transition={{ delay: 0.9 }}>
            <Card className="border-0 shadow-xl bg-gradient-to-br from-purple-50/50 to-pink-50/50 dark:from-purple-950/20 dark:to-pink-950/20 backdrop-blur-2xl">
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center">
                    <TrendingUp className="h-4 w-4 text-white" />
                  </div>
                  Solar Irradiance Analysis
                </CardTitle>
                <CardDescription>Daily solar radiation patterns throughout the year</CardDescription>
              </CardHeader>
              <CardContent>
                <IrradianceChart />
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      )}
    </motion.div>
  )
}
