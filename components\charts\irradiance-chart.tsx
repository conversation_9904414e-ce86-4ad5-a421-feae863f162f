"use client"
import { Composed<PERSON><PERSON>, Area, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

const data = [
  { month: "Jan", irradiance: 3.2, temperature: 45, cloudCover: 65 },
  { month: "Feb", irradiance: 4.1, temperature: 52, cloudCover: 58 },
  { month: "Mar", irradiance: 5.3, temperature: 61, cloudCover: 52 },
  { month: "Apr", irradiance: 6.8, temperature: 70, cloudCover: 45 },
  { month: "May", irradiance: 7.9, temperature: 78, cloudCover: 38 },
  { month: "Jun", irradiance: 8.4, temperature: 85, cloudCover: 32 },
  { month: "Jul", irradiance: 8.1, temperature: 88, cloudCover: 28 },
  { month: "Aug", irradiance: 7.3, temperature: 86, cloudCover: 35 },
  { month: "Sep", irradiance: 6.2, temperature: 79, cloudCover: 42 },
  { month: "Oct", irradiance: 4.8, temperature: 68, cloudCover: 48 },
  { month: "Nov", irradiance: 3.6, temperature: 56, cloudCover: 58 },
  { month: "Dec", irradiance: 2.9, temperature: 47, cloudCover: 68 },
]

export function IrradianceChart() {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-3 gap-4 text-center">
        <div>
          <div className="text-2xl font-bold text-purple-600">5.2</div>
          <div className="text-sm text-muted-foreground">Avg Peak Sun Hours</div>
        </div>
        <div>
          <div className="text-2xl font-bold text-blue-600">1,898</div>
          <div className="text-sm text-muted-foreground">kWh/m² Annual</div>
        </div>
        <div>
          <div className="text-2xl font-bold text-green-600">87%</div>
          <div className="text-sm text-muted-foreground">Clear Sky Days</div>
        </div>
      </div>

      <ChartContainer
        config={{
          irradiance: {
            label: "Solar Irradiance (kWh/m²/day)",
            color: "hsl(var(--chart-4))",
          },
          temperature: {
            label: "Temperature (°F)",
            color: "hsl(var(--chart-5))",
          },
        }}
        className="h-80"
      >
        <ResponsiveContainer width="100%" height="100%">
          <ComposedChart data={data}>
            <defs>
              <linearGradient id="irradianceGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#8b5cf6" stopOpacity={0.8} />
                <stop offset="95%" stopColor="#8b5cf6" stopOpacity={0.1} />
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
            <XAxis dataKey="month" stroke="#64748b" />
            <YAxis yAxisId="left" stroke="#64748b" />
            <YAxis yAxisId="right" orientation="right" stroke="#64748b" />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Area
              yAxisId="left"
              type="monotone"
              dataKey="irradiance"
              stroke="#8b5cf6"
              strokeWidth={2}
              fill="url(#irradianceGradient)"
            />
            <Line
              yAxisId="right"
              type="monotone"
              dataKey="temperature"
              stroke="#ef4444"
              strokeWidth={2}
              dot={{ fill: "#ef4444", strokeWidth: 2, r: 3 }}
            />
          </ComposedChart>
        </ResponsiveContainer>
      </ChartContainer>
    </div>
  )
}
