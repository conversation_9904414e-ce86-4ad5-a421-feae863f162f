"use client"

import * as React from "react"
import { Canvas } from "@react-three/fiber"
import { OrbitControls, Sky, Html } from "@react-three/drei"
import { Roof3DModel } from "./roof-3d-model"
import { SolarPanels } from "./solar-panels"

interface InteractiveRoofSceneProps {
  className?: string
}

export function InteractiveRoofScene({ className }: InteractiveRoofSceneProps) {
  const [selectedPanel, setSelectedPanel] = React.useState<number | null>(null)
  const [viewMode, setViewMode] = React.useState<"overview" | "detail">("overview")

  return (
    <div className={`relative w-full h-full ${className}`}>
      <Canvas
        camera={{ position: [12, 8, 12], fov: 45 }}
        shadows
        className="bg-gradient-to-b from-blue-200 to-blue-50 dark:from-blue-900 dark:to-blue-800"
      >
        {/* Enhanced Lighting */}
        <ambientLight intensity={0.3} />
        <directionalLight
          position={[15, 15, 10]}
          intensity={1.2}
          castShadow
          shadow-mapSize-width={4096}
          shadow-mapSize-height={4096}
          shadow-camera-far={100}
          shadow-camera-left={-20}
          shadow-camera-right={20}
          shadow-camera-top={20}
          shadow-camera-bottom={-20}
        />

        {/* Sky Environment */}
        <Sky distance={450000} sunPosition={[10, 10, 5]} inclination={0.49} azimuth={0.25} />

        {/* 3D Scene */}
        <Roof3DModel />
        <SolarPanels
          showLabels={viewMode === "detail"}
          selectedPanel={selectedPanel}
          onPanelSelect={setSelectedPanel}
        />

        {/* Enhanced Ground */}
        <mesh receiveShadow position={[0, -0.1, 0]} rotation={[-Math.PI / 2, 0, 0]}>
          <planeGeometry args={[40, 40]} />
          <meshLambertMaterial color="#22c55e" />
        </mesh>

        {/* Interactive Controls */}
        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          minDistance={8}
          maxDistance={25}
          maxPolarAngle={Math.PI / 2.1}
          autoRotate={false}
          autoRotateSpeed={0.5}
        />

        {/* Performance Stats */}
        <Html position={[-6, 5, 0]} transform occlude>
          <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm p-3 rounded-lg shadow-lg text-sm">
            <h4 className="font-semibold mb-2">System Performance</h4>
            <div className="space-y-1">
              <div className="flex justify-between gap-4">
                <span>Active Panels:</span>
                <span className="font-medium text-green-600">24/24</span>
              </div>
              <div className="flex justify-between gap-4">
                <span>Current Output:</span>
                <span className="font-medium">7.2 kW</span>
              </div>
              <div className="flex justify-between gap-4">
                <span>Efficiency:</span>
                <span className="font-medium">87.3%</span>
              </div>
            </div>
          </div>
        </Html>
      </Canvas>

      {/* View Mode Toggle */}
      <div className="absolute bottom-4 left-4">
        <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg p-2 flex gap-2">
          <button
            onClick={() => setViewMode("overview")}
            className={`px-3 py-1 rounded text-sm transition-colors ${
              viewMode === "overview"
                ? "bg-blue-500 text-white"
                : "text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
            }`}
          >
            Overview
          </button>
          <button
            onClick={() => setViewMode("detail")}
            className={`px-3 py-1 rounded text-sm transition-colors ${
              viewMode === "detail"
                ? "bg-blue-500 text-white"
                : "text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
            }`}
          >
            Detail View
          </button>
        </div>
      </div>

      {/* Instructions */}
      <div className="absolute top-4 left-4 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg p-3 text-sm max-w-xs">
        <h4 className="font-semibold mb-2">3D Controls</h4>
        <ul className="space-y-1 text-xs text-gray-600 dark:text-gray-300">
          <li>• Click and drag to rotate</li>
          <li>• Scroll to zoom in/out</li>
          <li>• Click panels for details</li>
          <li>• Right-click and drag to pan</li>
        </ul>
      </div>
    </div>
  )
}
