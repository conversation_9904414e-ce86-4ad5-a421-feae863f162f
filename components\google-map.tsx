"use client"

import * as React from "react"
import { MapPin, Satellite, Layers, Zap } from "lucide-react"
import { Button } from "@/components/ui/button"
import { motion } from "framer-motion"

interface GoogleMapProps {
  coordinates: { lat: number; lng: number } | null
  address: string
}

export function GoogleMap({ coordinates, address }: GoogleMapProps) {
  const [mapType, setMapType] = React.useState<"roadmap" | "satellite" | "hybrid">("satellite")
  const [showSolarData, setShowSolarData] = React.useState(false)

  return (
    <div className="relative w-full h-full bg-gradient-to-br from-blue-100 via-indigo-50 to-purple-100 dark:from-slate-800 dark:via-slate-700 dark:to-slate-600 rounded-2xl overflow-hidden">
      {/* Map Controls */}
      <div className="absolute top-4 right-4 z-10 flex flex-col gap-2">
        <div className="bg-white/90 dark:bg-slate-800/90 backdrop-blur-xl rounded-xl p-2 shadow-lg">
          <div className="flex gap-1">
            {[
              { type: "roadmap", icon: MapPin, label: "Map" },
              { type: "satellite", icon: Satellite, label: "Satellite" },
              { type: "hybrid", icon: Layers, label: "Hybrid" },
            ].map((option) => (
              <Button
                key={option.type}
                variant={mapType === option.type ? "default" : "ghost"}
                size="sm"
                onClick={() => setMapType(option.type as any)}
                className="h-8 px-3"
              >
                <option.icon className="h-3 w-3 mr-1" />
                {option.label}
              </Button>
            ))}
          </div>
        </div>

        <Button
          variant={showSolarData ? "default" : "outline"}
          size="sm"
          onClick={() => setShowSolarData(!showSolarData)}
          className="bg-white/90 dark:bg-slate-800/90 backdrop-blur-xl"
        >
          <Zap className="h-3 w-3 mr-1" />
          Solar Data
        </Button>
      </div>

      {/* Map Content */}
      <div className="relative w-full h-full flex items-center justify-center">
        {/* Animated background elements */}
        <div className="absolute inset-0">
          <motion.div
            animate={{
              scale: [1, 1.1, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 4,
              repeat: Number.POSITIVE_INFINITY,
              ease: "easeInOut",
            }}
            className="absolute top-10 left-10 w-32 h-32 bg-emerald-400/20 rounded-full blur-xl"
          />
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.2, 0.5, 0.2],
            }}
            transition={{
              duration: 6,
              repeat: Number.POSITIVE_INFINITY,
              ease: "easeInOut",
              delay: 2,
            }}
            className="absolute bottom-20 right-20 w-40 h-40 bg-blue-400/20 rounded-full blur-xl"
          />
          <motion.div
            animate={{
              scale: [1, 1.15, 1],
              opacity: [0.25, 0.55, 0.25],
            }}
            transition={{
              duration: 5,
              repeat: Number.POSITIVE_INFINITY,
              ease: "easeInOut",
              delay: 1,
            }}
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-36 h-36 bg-purple-400/20 rounded-full blur-xl"
          />
        </div>

        {coordinates ? (
          <div className="text-center space-y-6 z-10">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", damping: 15 }}
              className="inline-flex items-center justify-center w-24 h-24 rounded-full bg-gradient-to-br from-emerald-500 to-teal-600 shadow-2xl"
            >
              <MapPin className="h-12 w-12 text-white" />
            </motion.div>

            <div className="space-y-3">
              <motion.h3
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
                className="text-2xl font-bold text-gray-800 dark:text-gray-200"
              >
                Property Located!
              </motion.h3>
              <motion.p
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="text-gray-600 dark:text-gray-400 max-w-md mx-auto"
              >
                {address}
              </motion.p>
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.4 }}
                className="inline-flex items-center gap-2 px-6 py-3 bg-white/20 dark:bg-slate-800/20 backdrop-blur-xl rounded-full border border-white/30"
              >
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                <span className="text-sm font-medium">Google Maps Integration Active</span>
              </motion.div>
            </div>

            {showSolarData && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="mt-6 p-4 bg-white/10 dark:bg-slate-800/10 backdrop-blur-xl rounded-xl border border-white/20"
              >
                <h4 className="font-semibold mb-2">Solar Irradiance Data</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Peak Sun Hours:</span>
                    <span className="font-medium ml-2">5.2 hrs/day</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Annual Irradiance:</span>
                    <span className="font-medium ml-2">1,898 kWh/m²</span>
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        ) : (
          <div className="text-center space-y-6 z-10">
            <motion.div
              animate={{
                scale: [1, 1.1, 1],
                rotate: [0, 5, -5, 0],
              }}
              transition={{
                duration: 3,
                repeat: Number.POSITIVE_INFINITY,
                ease: "easeInOut",
              }}
              className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 shadow-xl"
            >
              <MapPin className="h-10 w-10 text-white" />
            </motion.div>

            <div className="space-y-3">
              <h3 className="text-2xl font-bold text-gray-700 dark:text-gray-300">Interactive Google Maps</h3>
              <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
                Enter an address above to view satellite imagery and solar analysis data
              </p>
              <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 dark:bg-slate-800/10 backdrop-blur-xl rounded-full">
                <Satellite className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium">Powered by Google Maps API</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Coordinate Display */}
      {coordinates && (
        <div className="absolute bottom-4 left-4 bg-white/90 dark:bg-slate-800/90 backdrop-blur-xl rounded-lg p-3 shadow-lg">
          <div className="text-xs text-muted-foreground">Coordinates</div>
          <div className="font-mono text-sm">
            {coordinates.lat.toFixed(6)}, {coordinates.lng.toFixed(6)}
          </div>
        </div>
      )}
    </div>
  )
}
