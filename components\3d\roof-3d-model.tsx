"use client"
import { useRef } from "react"
import type { Mesh } from "three"
import { useFrame } from "@react-three/fiber"

export function Roof3DModel() {
  const roofRef = useRef<Mesh>(null)
  const chimneyRef = useRef<Mesh>(null)

  // Subtle animation for the roof
  useFrame((state) => {
    if (roofRef.current) {
      roofRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.1) * 0.02
    }
  })

  return (
    <group>
      {/* Main Roof Structure */}
      <mesh ref={roofRef} castShadow receiveShadow position={[0, 1.5, 0]}>
        {/* Roof geometry - pitched roof */}
        <boxGeometry args={[8, 0.2, 6]} />
        <meshStandardMaterial color="#8b4513" roughness={0.8} />
      </mesh>

      {/* Roof Peak */}
      <mesh castShadow position={[0, 2.2, 0]} rotation={[0, 0, Math.PI / 2]}>
        <coneGeometry args={[0.15, 8, 4]} />
        <meshStandardMaterial color="#654321" />
      </mesh>

      {/* House Base */}
      <mesh castShadow receiveShadow position={[0, 0.5, 0]}>
        <boxGeometry args={[7, 1, 5]} />
        <meshStandardMaterial color="#f5f5dc" roughness={0.7} />
      </mesh>

      {/* Chimney */}
      <mesh ref={chimneyRef} castShadow position={[2.5, 2.5, -1.5]}>
        <boxGeometry args={[0.8, 1.5, 0.8]} />
        <meshStandardMaterial color="#8b0000" roughness={0.9} />
      </mesh>

      {/* Roof Edges */}
      <mesh castShadow position={[0, 1.65, 3.1]}>
        <boxGeometry args={[8.2, 0.1, 0.2]} />
        <meshStandardMaterial color="#654321" />
      </mesh>
      <mesh castShadow position={[0, 1.65, -3.1]}>
        <boxGeometry args={[8.2, 0.1, 0.2]} />
        <meshStandardMaterial color="#654321" />
      </mesh>

      {/* Gutters */}
      <mesh castShadow position={[0, 1.4, 3.2]}>
        <boxGeometry args={[8, 0.05, 0.15]} />
        <meshStandardMaterial color="#c0c0c0" metalness={0.8} roughness={0.2} />
      </mesh>
      <mesh castShadow position={[0, 1.4, -3.2]}>
        <boxGeometry args={[8, 0.05, 0.15]} />
        <meshStandardMaterial color="#c0c0c0" metalness={0.8} roughness={0.2} />
      </mesh>
    </group>
  )
}
