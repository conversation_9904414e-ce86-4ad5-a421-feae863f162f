"use client"

import * as React from "react"
import { Search, MapPin, Navigation, Zap, Home, FileText } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { GoogleMap } from "@/components/google-map"
import { motion, AnimatePresence } from "framer-motion"

export function AddressInput() {
  const [address, setAddress] = React.useState("")
  const [isSearching, setIsSearching] = React.useState(false)
  const [coordinates, setCoordinates] = React.useState<{ lat: number; lng: number } | null>(null)

  const handleSearch = async () => {
    if (!address.trim()) return

    setIsSearching(true)

    // Simulate API call with realistic timing
    setTimeout(() => {
      setCoordinates({ lat: 37.7749, lng: -122.4194 }) // San Francisco coordinates
      setIsSearching(false)
    }, 2500)
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        damping: 20,
        stiffness: 100,
      },
    },
  }

  return (
    <motion.div variants={containerVariants} initial="hidden" animate="visible" className="p-8 space-y-12 relative">
      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          animate={{
            y: [0, -20, 0],
            rotate: [0, 5, 0],
          }}
          transition={{
            duration: 6,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
          }}
          className="absolute top-20 right-20 w-32 h-32 bg-gradient-to-br from-emerald-400/20 to-teal-600/20 rounded-full blur-xl"
        />
        <motion.div
          animate={{
            y: [0, 15, 0],
            rotate: [0, -3, 0],
          }}
          transition={{
            duration: 8,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
            delay: 2,
          }}
          className="absolute bottom-40 left-10 w-24 h-24 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-xl"
        />
      </div>

      {/* Hero Section */}
      <motion.div variants={itemVariants} className="text-center space-y-6 py-12">
        <motion.div
          whileHover={{ scale: 1.05, rotate: 5 }}
          transition={{ type: "spring", damping: 15 }}
          className="inline-flex items-center justify-center w-20 h-20 rounded-3xl bg-gradient-to-br from-emerald-500 via-teal-500 to-blue-500 shadow-2xl"
        >
          <MapPin className="h-10 w-10 text-white" />
        </motion.div>

        <div className="space-y-4">
          <motion.h1
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-emerald-600 via-teal-600 to-blue-600 bg-clip-text text-transparent"
          >
            Discover Your Solar Future
          </motion.h1>
          <motion.p
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="text-xl md:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed"
          >
            Transform sunlight into savings. Enter your address to unlock personalized solar insights powered by
            cutting-edge AI and satellite imagery.
          </motion.p>
        </div>
      </motion.div>

      {/* Search Section */}
      <motion.div variants={itemVariants} className="max-w-5xl mx-auto">
        <Card className="border-0 shadow-2xl bg-white/20 dark:bg-slate-900/20 backdrop-blur-2xl overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/10 via-teal-500/10 to-blue-500/10" />
          <CardHeader className="text-center pb-8 relative">
            <CardTitle className="text-3xl flex items-center justify-center gap-4">
              <motion.div
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.5 }}
                className="h-10 w-10 rounded-xl bg-gradient-to-br from-emerald-500 to-teal-600 flex items-center justify-center shadow-lg"
              >
                <Search className="h-5 w-5 text-white" />
              </motion.div>
              Property Address Search
            </CardTitle>
            <CardDescription className="text-lg mt-2">
              Enter your complete address to begin the solar analysis journey
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-8 relative">
            <div className="flex gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-4 top-1/2 h-6 w-6 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="123 Solar Street, Green City, State 12345"
                  value={address}
                  onChange={(e) => setAddress(e.target.value)}
                  onKeyPress={(e) => e.key === "Enter" && handleSearch()}
                  className="pl-14 h-16 text-lg border-2 border-white/20 focus:border-emerald-500 rounded-2xl bg-white/10 dark:bg-slate-800/10 backdrop-blur-xl transition-all duration-300"
                  disabled={isSearching}
                />
              </div>
              <Button
                onClick={handleSearch}
                disabled={isSearching || !address.trim()}
                className="h-16 px-8 bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                <AnimatePresence mode="wait">
                  {isSearching ? (
                    <motion.div
                      key="searching"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      className="flex items-center gap-3"
                    >
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{
                          duration: 1,
                          repeat: Number.POSITIVE_INFINITY,
                          ease: "linear",
                        }}
                        className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                      />
                      <motion.span
                        animate={{ opacity: [1, 0.5, 1] }}
                        transition={{
                          duration: 1.5,
                          repeat: Number.POSITIVE_INFINITY,
                          ease: "easeInOut",
                        }}
                      >
                        Searching...
                      </motion.span>
                    </motion.div>
                  ) : (
                    <motion.div
                      key="search"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      className="flex items-center gap-3"
                    >
                      <Navigation className="h-6 w-6" />
                      Find Property
                    </motion.div>
                  )}
                </AnimatePresence>
              </Button>
            </div>

            {/* Quick Examples */}
            <div className="flex flex-wrap gap-3 justify-center">
              <span className="text-sm text-muted-foreground">Quick examples:</span>
              {[
                "1600 Pennsylvania Ave, Washington DC",
                "1 Apple Park Way, Cupertino CA",
                "350 5th Ave, New York NY",
              ].map((example, index) => (
                <motion.button
                  key={example}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setAddress(example)}
                  disabled={isSearching}
                  className="text-xs px-4 py-2 rounded-full bg-white/10 dark:bg-slate-800/10 border border-white/20 hover:bg-white/20 dark:hover:bg-slate-700/20 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {example}
                </motion.button>
              ))}
            </div>

            {/* Loading Progress */}
            <AnimatePresence>
              {isSearching && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="bg-white/10 dark:bg-slate-800/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20"
                >
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Searching property...</span>
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
                      >
                        <Search className="h-4 w-4 text-emerald-500" />
                      </motion.div>
                    </div>
                    <div className="w-full bg-white/20 rounded-full h-2">
                      <motion.div
                        className="bg-gradient-to-r from-emerald-500 to-teal-600 h-2 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ duration: 2.5, ease: "easeInOut" }}
                      />
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-xs text-center">
                      <motion.div
                        initial={{ opacity: 0.3 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.5 }}
                        className="space-y-1"
                      >
                        <div className="text-lg">🗺️</div>
                        <p>Geocoding</p>
                      </motion.div>
                      <motion.div
                        initial={{ opacity: 0.3 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 1 }}
                        className="space-y-1"
                      >
                        <div className="text-lg">🛰️</div>
                        <p>Satellite Data</p>
                      </motion.div>
                      <motion.div
                        initial={{ opacity: 0.3 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 1.5 }}
                        className="space-y-1"
                      >
                        <div className="text-lg">✅</div>
                        <p>Validation</p>
                      </motion.div>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </CardContent>
        </Card>
      </motion.div>

      {/* Google Map Section */}
      <motion.div variants={itemVariants} className="max-w-7xl mx-auto">
        <Card className="border-0 shadow-2xl bg-white/20 dark:bg-slate-900/20 backdrop-blur-2xl overflow-hidden">
          <CardHeader>
            <CardTitle className="flex items-center gap-3 text-2xl">
              <motion.div
                whileHover={{ scale: 1.1 }}
                className="h-10 w-10 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-lg"
              >
                <MapPin className="h-5 w-5 text-white" />
              </motion.div>
              Interactive Property Map
            </CardTitle>
            <CardDescription className="text-lg">
              Satellite view of your property with solar analysis overlay
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <div className="aspect-video w-full">
              <GoogleMap coordinates={coordinates} address={address} />
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Features Preview */}
      <motion.div variants={itemVariants} className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">What's Next?</h2>
          <p className="text-lg text-muted-foreground">
            Your solar journey continues with these powerful analysis tools
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {[
            {
              icon: Home,
              title: "AI Roof Detection",
              description:
                "Advanced computer vision analyzes your roof structure, pitch, and optimal panel placement zones",
              gradient: "from-blue-500 via-purple-500 to-indigo-600",
              delay: 0,
            },
            {
              icon: Zap,
              title: "Solar Potential Analysis",
              description:
                "Calculate precise energy production, savings, and environmental impact using real weather data",
              gradient: "from-amber-500 via-orange-500 to-red-500",
              delay: 0.1,
            },
            {
              icon: FileText,
              title: "Professional Report",
              description: "Generate comprehensive PDF reports with 3D visualizations and financial projections",
              gradient: "from-purple-500 via-pink-500 to-rose-500",
              delay: 0.2,
            },
          ].map((feature, index) => (
            <motion.div
              key={index}
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: feature.delay, type: "spring", damping: 20 }}
              whileHover={{ y: -10, scale: 1.02 }}
              className="group"
            >
              <Card className="border-0 shadow-xl bg-white/10 dark:bg-slate-900/10 backdrop-blur-2xl hover:shadow-2xl transition-all duration-500 h-full overflow-hidden">
                <div
                  className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-500`}
                />
                <CardContent className="p-8 text-center space-y-6 relative">
                  <motion.div
                    whileHover={{ rotate: 360, scale: 1.1 }}
                    transition={{ duration: 0.5 }}
                    className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br ${feature.gradient} shadow-xl`}
                  >
                    <feature.icon className="h-8 w-8 text-white" />
                  </motion.div>
                  <div className="space-y-3">
                    <h3 className="text-xl font-bold">{feature.title}</h3>
                    <p className="text-muted-foreground leading-relaxed">{feature.description}</p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </motion.div>
  )
}
