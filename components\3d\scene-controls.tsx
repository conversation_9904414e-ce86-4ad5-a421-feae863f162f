"use client"
import { <PERSON>, <PERSON><PERSON>ff, RotateCcw, Zap, ZapOff, Info } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"

interface SceneControlsProps {
  showPanels: boolean
  onTogglePanels: (show: boolean) => void
  showLabels: boolean
  onToggleLabels: (show: boolean) => void
  onResetView: () => void
}

export function SceneControls({
  showPanels,
  onTogglePanels,
  showLabels,
  onToggleLabels,
  onResetView,
}: SceneControlsProps) {
  return (
    <div className="absolute top-4 right-4 space-y-2">
      <Card className="p-3 bg-white/90 dark:bg-slate-800/90 backdrop-blur-xl border-white/20 shadow-xl">
        <div className="flex flex-col gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onTogglePanels(!showPanels)}
            className={`justify-start h-9 ${showPanels ? "bg-blue-100 dark:bg-blue-900/50" : ""}`}
          >
            {showPanels ? <Zap className="h-4 w-4 mr-2 text-blue-600" /> : <ZapOff className="h-4 w-4 mr-2" />}
            {showPanels ? "Hide Panels" : "Show Panels"}
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => onToggleLabels(!showLabels)}
            className={`justify-start h-9 ${showLabels ? "bg-green-100 dark:bg-green-900/50" : ""}`}
            disabled={!showPanels}
          >
            {showLabels ? <EyeOff className="h-4 w-4 mr-2 text-green-600" /> : <Eye className="h-4 w-4 mr-2" />}
            {showLabels ? "Hide Labels" : "Show Labels"}
          </Button>

          <Button variant="ghost" size="sm" onClick={onResetView} className="justify-start h-9">
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset View
          </Button>
        </div>
      </Card>

      <Card className="p-3 bg-white/90 dark:bg-slate-800/90 backdrop-blur-xl border-white/20 shadow-xl">
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <Info className="h-3 w-3" />
          <span>Click panels for details</span>
        </div>
      </Card>
    </div>
  )
}
