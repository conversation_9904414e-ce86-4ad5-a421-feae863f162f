"use client"

import * as React from "react"
import { Canvas } from "@react-three/fiber"
import { OrbitControls, Html, Sky } from "@react-three/drei"
import { Roof3DModel } from "./roof-3d-model"
import { SolarPanels } from "./solar-panels"
import { SceneControls } from "./scene-controls"

export function Roof3DVisualization() {
  const [showPanels, setShowPanels] = React.useState(true)
  const [showLabels, setShowLabels] = React.useState(false)
  const [selectedPanel, setSelectedPanel] = React.useState<number | null>(null)

  return (
    <div className="relative w-full h-full rounded-xl overflow-hidden">
      <Canvas
        camera={{ position: [8, 6, 8], fov: 50 }}
        shadows
        className="bg-gradient-to-b from-sky-300 via-sky-200 to-sky-100 dark:from-sky-900 dark:via-sky-800 dark:to-sky-700"
      >
        {/* Enhanced Lighting */}
        <ambientLight intensity={0.6} />
        <directionalLight
          position={[10, 10, 5]}
          intensity={1.5}
          castShadow
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
          shadow-camera-far={50}
          shadow-camera-left={-10}
          shadow-camera-right={10}
          shadow-camera-top={10}
          shadow-camera-bottom={-10}
        />

        {/* Beautiful Sky */}
        <Sky distance={450000} sunPosition={[10, 10, 5]} inclination={0.49} azimuth={0.25} />

        {/* 3D Models */}
        <Roof3DModel />
        {showPanels && (
          <SolarPanels showLabels={showLabels} selectedPanel={selectedPanel} onPanelSelect={setSelectedPanel} />
        )}

        {/* Enhanced Ground */}
        <mesh receiveShadow position={[0, -0.1, 0]} rotation={[-Math.PI / 2, 0, 0]}>
          <planeGeometry args={[30, 30]} />
          <meshLambertMaterial color="#22c55e" />
        </mesh>

        {/* Controls */}
        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          minDistance={5}
          maxDistance={20}
          maxPolarAngle={Math.PI / 2.2}
        />

        {/* Panel Info */}
        {selectedPanel !== null && (
          <Html position={[0, 4, 0]} center>
            <div className="bg-white/95 dark:bg-slate-800/95 backdrop-blur-xl p-4 rounded-xl shadow-2xl border border-white/20 min-w-56">
              <h4 className="font-bold text-lg mb-3 text-center">Panel #{selectedPanel + 1}</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between items-center p-2 bg-blue-50 dark:bg-blue-950/50 rounded-lg">
                  <span>Power Output:</span>
                  <span className="font-bold text-blue-600">350W</span>
                </div>
                <div className="flex justify-between items-center p-2 bg-green-50 dark:bg-green-950/50 rounded-lg">
                  <span>Efficiency:</span>
                  <span className="font-bold text-green-600">21.2%</span>
                </div>
                <div className="flex justify-between items-center p-2 bg-amber-50 dark:bg-amber-950/50 rounded-lg">
                  <span>Annual Generation:</span>
                  <span className="font-bold text-amber-600">518 kWh</span>
                </div>
              </div>
            </div>
          </Html>
        )}
      </Canvas>

      {/* Enhanced Scene Controls */}
      <SceneControls
        showPanels={showPanels}
        onTogglePanels={setShowPanels}
        showLabels={showLabels}
        onToggleLabels={setShowLabels}
        onResetView={() => setSelectedPanel(null)}
      />
    </div>
  )
}
