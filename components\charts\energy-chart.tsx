"use client"
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, ResponsiveContainer } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

const data = [
  { month: "Jan", production: 850, consumption: 920 },
  { month: "Feb", production: 950, consumption: 880 },
  { month: "Mar", production: 1100, consumption: 850 },
  { month: "Apr", production: 1250, consumption: 800 },
  { month: "May", production: 1380, consumption: 780 },
  { month: "Jun", production: 1420, consumption: 850 },
  { month: "Jul", production: 1450, consumption: 920 },
  { month: "Aug", production: 1380, consumption: 900 },
  { month: "Sep", production: 1200, consumption: 820 },
  { month: "Oct", production: 1050, consumption: 840 },
  { month: "Nov", production: 900, consumption: 880 },
  { month: "Dec", production: 800, consumption: 950 },
]

export function EnergyChart() {
  return (
    <div className="space-y-4">
      <div className="text-center">
        <div className="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
          12,450 kWh
        </div>
        <div className="text-sm text-muted-foreground">Annual Production</div>
      </div>

      <ChartContainer
        config={{
          production: {
            label: "Solar Production",
            color: "hsl(var(--chart-1))",
          },
          consumption: {
            label: "Home Consumption",
            color: "hsl(var(--chart-2))",
          },
        }}
        className="h-64"
      >
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={data}>
            <defs>
              <linearGradient id="productionGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#10b981" stopOpacity={0.8} />
                <stop offset="95%" stopColor="#10b981" stopOpacity={0.1} />
              </linearGradient>
              <linearGradient id="consumptionGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#f59e0b" stopOpacity={0.8} />
                <stop offset="95%" stopColor="#f59e0b" stopOpacity={0.1} />
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
            <XAxis dataKey="month" stroke="#64748b" />
            <YAxis stroke="#64748b" />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Area
              type="monotone"
              dataKey="production"
              stroke="#10b981"
              strokeWidth={2}
              fill="url(#productionGradient)"
            />
            <Area
              type="monotone"
              dataKey="consumption"
              stroke="#f59e0b"
              strokeWidth={2}
              fill="url(#consumptionGradient)"
            />
          </AreaChart>
        </ResponsiveContainer>
      </ChartContainer>
    </div>
  )
}
